from django.views.generic import CreateView
from django.contrib.auth.views import <PERSON>ginView, LogoutView
from django.contrib.auth import logout
from django.shortcuts import redirect
from django.urls import reverse_lazy
from .forms import SignUpForm


class SignupView(CreateView):
    template_name = "registration/signup.html"
    form_class = SignUpForm
    success_url = reverse_lazy("users:login")


class UserLoginView(LoginView):
    template_name = "registration/login.html"
    redirect_authenticated_user = True


class UserLogoutView(LogoutView):
    """Custom logout view that accepts GET requests"""

    http_method_names = ["get", "post"]
    next_page = "snippets:explore"  # Explicit redirect after logout

    def dispatch(self, request, *args, **kwargs):
        """Ensure proper logout and redirect"""
        if request.user.is_authenticated:
            logout(request)
        return redirect("snippets:explore")
