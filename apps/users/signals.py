from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Profile
from django.conf import settings


@receiver(post_save, sender=settings.AUTH_USER_MODEL)
def create_profile(sender, instance, created, **kwargs):
    if created:
        Profile.objects.create(user=instance)


@receiver(post_save, sender=settings.AUTH_USER_MODEL)
def save_profile(sender, instance, **kwargs):
    # one-to-one relationship, so we can access the profile from the user
    # by doing instance.profile.bio = "..."
    instance.profile.save()
